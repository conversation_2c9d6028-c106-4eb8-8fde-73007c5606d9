package com.boryou.web.module.socket.handle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.boryou.web.module.socket.cache.SocketManagerCache;
import com.boryou.web.module.socket.domain.SocketMessage;
import com.boryou.web.module.socket.domain.TransferMessage;
import com.boryou.web.module.socket.enums.SocketChannelEnum;
import com.boryou.web.module.socket.service.WebsocketRedisService;
import com.boryou.web.module.socket.util.SocketUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.boryou.web.module.socket.cache.SocketManagerCache.LOGIN_ID;

@Component
@Slf4j
@RequiredArgsConstructor
public class WebSocketHandle extends TextWebSocketHandler {

    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            16, 16, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(5000),
            r -> {
                Thread t = new Thread(r, "ws-sender-" + r.hashCode());
                t.setDaemon(true); // 设置为守护线程，避免阻止JVM关闭
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy());

    private final WebsocketRedisService websocketRedisService;

    /**
     * 接受客户端消息
     *
     * @param session session
     * @param message message
     * @throws IOException e
     */
    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws IOException {
        String payload = message.getPayload();
        if (CharSequenceUtil.isBlank(payload)) {
            return;
        }
        if (CharSequenceUtil.equals(payload, SocketChannelEnum.PING.getName())) {
            // 处理前端发送的心跳消息，返回 pong 响应
            // log.info("收到客户端心跳消息: , sessionId: {}", session.getId());
            // 构造并发送 pong 响应
            session.sendMessage(new TextMessage(SocketChannelEnum.PONG.getName()));
            return;
        }
        SocketMessage msg = JSONUtil.toBean(message.getPayload(), SocketMessage.class);
        assert msg != null;
        SocketChannelEnum channel = msg.getChannel();
        switch (channel) {
            case PING:
                break;
            default:
                TransferMessage tm = new TransferMessage();
                tm.setMessage(msg);
                String channelUsername = (String) session.getAttributes().get(LOGIN_ID);
                if (channelUsername != null) {
                    tm.setFromUser(channelUsername);
                }
                // 将消息透传给服务端，服务端需要注意消息的幂等处理
                websocketRedisService.sendWsToService(tm);
                break;
        }
    }

    /**
     * 连接建立后
     *
     * @param session session
     * @throws Exception e
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        DateTime date = DateUtil.date();
        String loginId = (String) session.getAttributes().get(LOGIN_ID);
        session = new ConcurrentWebSocketSessionDecorator(session,
                1000 * 10,
                1024 * 200,
                ConcurrentWebSocketSessionDecorator.OverflowStrategy.TERMINATE);
        SocketManagerCache.addOnlineSid(loginId, session);
        websocketRedisService.online(loginId, date);
        // log.warn("建立连接: {}", loginId);
    }

    /**
     * 连接关闭后
     *
     * @param session session
     * @param status  status
     * @throws Exception e
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        userOffLine(session);
    }

    private void userOffLine(WebSocketSession session) throws IOException {
        SocketManagerCache.removed(session);
        if (session.isOpen()) {
            session.close();
        }
    }

    /**
     * 根据loginId 推送消息
     *
     * @param loginIds      登陆用户Id集合
     * @param socketMessage 消息对象
     */
    public void sendMessage(List<String> loginIds, SocketMessage socketMessage) {
        // TimeInterval timeInterval = new TimeInterval();
        if (CollUtil.isEmpty(loginIds)) {
            return;
        }
        String nanoId = IdUtil.nanoId(10);
        for (String loginId : loginIds) {
            pushLoginId(socketMessage, loginId, nanoId);
        }
        // log.warn("[{}] sendMessage 完成,耗时:{}", nanoId, timeInterval.intervalMs());
    }

    private void pushLoginId(SocketMessage socketMessage, String loginId, String nanoId) {
        CopyOnWriteArrayList<WebSocketSession> webSocketSessions = SocketManagerCache.ONLINE_UID_MAP.get(loginId);
        if (CollUtil.isEmpty(webSocketSessions)) {
            // log.warn("[{}] 内存中不存在session, loginId: {}", nanoId, loginId);
            return;
        }
        String message = SocketUtil.transferMessage(socketMessage);
        if (CharSequenceUtil.isBlank(message)) {
            log.warn("[{}] 消息为空, loginId: {}", nanoId, loginId);
            return;
        }

        // 创建消息对象，在线程池中复用
        final TextMessage textMessage = new TextMessage(message);

        // 复制会话列表，避免在遍历过程中发生变化
        List<WebSocketSession> sessionsCopy = new CopyOnWriteArrayList<>(webSocketSessions);

        for (WebSocketSession session : sessionsCopy) {
            if (!session.isOpen()) {
                log.warn("[{}] Session {} 已关闭", nanoId, session);
                // SocketManagerCache.removed(session);
                continue;
            }

            // 在发送消息时
            // logThreadPoolStatus();

            threadPool.execute(() -> {
                try {
                    session.sendMessage(textMessage);
                } catch (Exception e) {
                    log.error("[{}] session: {}, 发送消息失败: {}", nanoId, session, e.getMessage(), e);
                    // SocketManagerCache.removed(session);
                }
            });
        }

    }

    /**
     * 打印线程池状态信息
     */
    private void logThreadPoolStatus() {
        int activeCount = threadPool.getActiveCount();
        int poolSize = threadPool.getPoolSize();
        int corePoolSize = threadPool.getCorePoolSize();
        int maximumPoolSize = threadPool.getMaximumPoolSize();
        long completedTaskCount = threadPool.getCompletedTaskCount();
        long taskCount = threadPool.getTaskCount();
        int queueSize = threadPool.getQueue().size();

        log.info("WebSocket线程池状态: 活跃线程数={}, 当前线程数={}, 核心线程数={}, 最大线程数={}, " +
                        "已完成任务数={}, 总任务数={}, 队列中任务数={}",
                activeCount, poolSize, corePoolSize, maximumPoolSize,
                completedTaskCount, taskCount, queueSize);
    }

}
