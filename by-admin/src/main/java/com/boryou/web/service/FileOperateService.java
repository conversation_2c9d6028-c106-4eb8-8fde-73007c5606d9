package com.boryou.web.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

@Service
@Slf4j
public class FileOperateService {

    public String importDoc(MultipartFile file) {
        String text = "";
        String name = "";
        try {
            // 获取文件后缀名
            String fileName = file.getOriginalFilename();
            if (CharSequenceUtil.isBlank(fileName)) {
                throw new CustomException("文件名不能为空");
            }
            if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
                name = fileName.substring(fileName.lastIndexOf(".") + 1);
            }
            InputStream inputStream = file.getInputStream();

            // 非utf-8编码，进行转码
            ZipSecureFile.setMinInflateRatio(-1.0d);
            if (name.contains("docx")) {
                text = this.getContentDocx(inputStream);
                if (CharSequenceUtil.isBlank(text)) {
                    text = this.getContentDoc(inputStream);
                }
            } else if (name.contains("doc")) {
                text = this.getContentDoc(inputStream);
                if (CharSequenceUtil.isBlank(text)) {
                    text = this.getContentDocx(inputStream);
                }
            }
            inputStream.close();
            log.warn("文件名为: {}, 转换文本为: {}", fileName, text);
        } catch (Exception e) {
            log.error("上传失败: {}", e.getMessage(), e);
            throw new CustomException("上传失败");
        }
        return text;
    }

    /**
     * 获取正文文件内容，docx方法
     *
     * @param file
     * @return
     */
    public String getContentDocx(InputStream is) {
        StringBuilder content = new StringBuilder();
        try (XWPFDocument xwpf = new XWPFDocument(is)) {
            // 2007版本的word
            // 2007版本，仅支持docx文件处理
            List<XWPFParagraph> paragraphs = xwpf.getParagraphs();
            if (!paragraphs.isEmpty()) {
                for (XWPFParagraph paragraph : paragraphs) {
                    if (!paragraph.getParagraphText().startsWith("    ")) {
                        content.append(paragraph.getParagraphText().trim()).append("\n\t");
                    } else {
                        content.append(paragraph.getParagraphText());
                    }
                }
            }
        } catch (Exception e) {
            log.error("docx解析正文异常:{}", e.getMessage(), e);
        }
        return content.toString();
    }

    /**
     * 获取正文文件内容，doc方法
     *
     * @param is 输入流，不能为null
     * @return 解析后的文档内容，如果解析失败返回空字符串
     * @throws CustomException 当输入流为null或解析过程中发生严重错误时抛出
     */
    public String getContentDoc(InputStream is) {
        if (is == null) {
            throw new CustomException("输入流不能为空");
        }

        StringBuilder content = new StringBuilder();
        try (WordExtractor extractor = new WordExtractor(is)) {
            // 处理2003版本的Word文档(.doc格式)
            String[] paragraphTexts = extractor.getParagraphText();

            if (paragraphTexts != null && paragraphTexts.length > 0) {
                processParagraphs(content, paragraphTexts);
            } else {
                log.warn("文档中未找到任何段落内容");
            }

        } catch (Exception e) {
            log.error("doc文档解析失败: {}", e.getMessage(), e);
            // throw new CustomException("doc文档解析失败: " + e.getMessage());
        }

        return content.toString().trim();
    }

    /**
     * 处理段落文本，添加适当的格式
     *
     * @param content 内容构建器
     * @param paragraphTexts 段落文本数组
     */
    private void processParagraphs(StringBuilder content, String[] paragraphTexts) {
        final String INDENT_PREFIX = "    ";
        final String LINE_SEPARATOR = System.lineSeparator();
        final String DEFAULT_INDENT = "        ";

        for (String paragraph : paragraphTexts) {
            if (CharSequenceUtil.isBlank(paragraph)) {
                continue; // 跳过空段落
            }

            // 如果段落不是以缩进开始，则添加换行和默认缩进
            if (!paragraph.startsWith(INDENT_PREFIX)) {
                String trimmedParagraph = paragraph.trim();
                if (CharSequenceUtil.isNotBlank(trimmedParagraph)) {
                    content.append(trimmedParagraph)
                           .append(LINE_SEPARATOR)
                           .append(DEFAULT_INDENT);
                }
            } else {
                // 保持原有缩进格式
                content.append(paragraph);
            }
        }
    }

}
